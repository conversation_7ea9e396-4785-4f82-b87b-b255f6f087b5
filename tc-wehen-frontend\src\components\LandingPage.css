/* TC-<PERSON>hen Minimalist Landing Page Styles */

.landing-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--gray-900);
  background: var(--white);
}

/* Clean Header Styles */
.header {
  background: var(--white);
  color: var(--gray-900);
  padding: 1rem 0;
  box-shadow: var(--shadow-sm);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all 0.3s ease;
  border-bottom: 1px solid var(--gray-200);
}

.header-visible {
  transform: translateY(0);
  opacity: 1;
}

.header-hidden {
  transform: translateY(-100%);
  opacity: 0.95;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: var(--gray-900);
  flex-shrink: 0;
  text-decoration: none;
}

.logo-img {
  height: 50px;
  width: 50px;
  border-radius: 50%;
  object-fit: contain;
  background: var(--gray-50);
  padding: 4px;
  border: 1px solid var(--gray-200);
}

.logo h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  color: var(--gray-900);
  letter-spacing: -0.025em;
}

/* Clean Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 32px;
  height: 32px;
  background: var(--white);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  cursor: pointer;
  padding: 4px;
  z-index: 1001;
  transition: all 0.2s ease;
}

.mobile-menu-toggle:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background-color: var(--gray-700);
  border-radius: 1px;
  transition: all 0.3s ease;
  transform-origin: center;
}

.hamburger-line.open:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-line.open:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}

.hamburger-line.open:nth-child(3) {
  transform: rotate(-45deg) translate(5px, -5px);
}

/* Clean Navigation Styles */
.nav {
  display: flex;
}

/* Desktop Navigation */
.desktop-nav {
  display: flex;
}

/* Mobile Navigation - Hidden by default */
.mobile-nav {
  display: none;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 0.5rem;
  align-items: center;
  margin: 0;
  padding: 0;
}

.nav-list a {
  color: var(--gray-700);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: 0.95rem;
}

.nav-list a:hover,
.nav-list a.active {
  color: var(--gray-900);
  background: var(--gray-100);
}

/* Clean Dropdown Styles */
.dropdown {
  position: relative;
}

.dropdown-arrow {
  font-size: 0.75rem;
  margin-left: 0.5rem;
  transition: transform 0.2s ease;
}

.dropdown-open .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--white);
  border: 1px solid var(--gray-200);
  min-width: 200px;
  box-shadow: var(--shadow-lg);
  border-radius: var(--radius-lg);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px);
  transition: all 0.2s ease;
  z-index: 1000;
  overflow: hidden;
}

.dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-menu li {
  list-style: none;
}

.dropdown-menu a {
  color: var(--gray-700) !important;
  display: block;
  padding: 0.75rem 1rem;
  font-weight: 500;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--gray-100);
}

.dropdown-menu a:last-child {
  border-bottom: none;
}

.dropdown-menu a:hover {
  background-color: var(--gray-50);
  color: var(--gray-900) !important;
}

/* Clean Booking Button Styling */
.booking-button {
  background: var(--tc-yellow-orange) !important;
  color: var(--white) !important;
  font-weight: 600 !important;
  box-shadow: var(--shadow-sm) !important;
  border: none !important;
  border-radius: var(--radius-md) !important;
  padding: 0.75rem 1.5rem !important;
}

.booking-button:hover {
  background: var(--tc-yellow-orange-dark) !important;
  box-shadow: var(--shadow-md) !important;
}

/* Main Content */
.main {
  flex: 1;
  padding-top: 70px; /* Account for fixed header */
}

/* Clean Page Layout */
.page {
  min-height: calc(100vh - 70px);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  padding: 3rem 0;
}

.page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  z-index: 1;
}

/* Clean Content Container */
.content {
  position: relative;
  z-index: 2;
  max-width: 1000px;
  margin: 0 auto;
  padding: 3rem;
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

/* Hero Section Styles - Inspired by TC Sandanger */
.hero-section {
  min-height: 70vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-content {
  text-align: center;
  color: var(--white);
  max-width: 800px;
  padding: 2rem;
  z-index: 2;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-shadow: none;
  letter-spacing: -0.025em;
  line-height: 1.1;
  color: #000000 !important;
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 2.5rem;
  text-shadow: none;
  font-weight: 400;
  line-height: 1.4;
  color: #000000 !important;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.hero-button {
  padding: 1rem 2rem;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  min-width: 180px;
}

.hero-button.primary {
  background: var(--tc-yellow-orange);
  color: #000000;
  box-shadow: 0 4px 12px rgba(255, 165, 0, 0.4), 0 2px 4px rgba(0, 0, 0, 0.3);
  border: 2px solid var(--tc-yellow-orange);
}

.hero-button.primary:hover {
  background: var(--tc-yellow-orange-dark);
  border-color: var(--tc-yellow-orange-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 165, 0, 0.5), 0 4px 8px rgba(0, 0, 0, 0.4);
}

.hero-button.secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #000000;
  border: 2px solid var(--white);
  backdrop-filter: blur(10px);
}

.hero-button.secondary:hover {
  background: var(--white);
  color: var(--gray-900);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

/* Clean Home Page */
.home-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem 2rem;
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

.welcome-message {
  text-align: center;
  background: var(--white);
  padding: 4rem 2rem;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  border-top: 3px solid var(--tc-yellow-orange);
}

.welcome-message h2 {
  font-size: 3rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 1rem;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.welcome-subtitle {
  font-size: 1.25rem;
  color: var(--gray-600);
  margin: 0;
  font-weight: 400;
  line-height: 1.5;
}

/* Clean News Section */
.news-section {
  background: var(--white);
  padding: 3rem 2rem;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  border-top: 3px solid var(--tc-blue);
}

.news-section h3 {
  color: var(--gray-900);
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 2rem;
  text-align: center;
  letter-spacing: -0.025em;
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.news-item {
  background: var(--gray-50);
  padding: 1.5rem;
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
  transition: box-shadow 0.2s ease;
}

.news-item:hover {
  box-shadow: var(--shadow-md);
}

.news-date {
  color: var(--tc-yellow-orange);
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.news-item h4 {
  color: var(--gray-900);
  font-size: 1.25rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  letter-spacing: -0.025em;
  line-height: 1.3;
}

.news-item p {
  color: var(--gray-700);
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
}

/* Latest News Styles - Enhanced with Images */
.latest-news {
  margin-bottom: 2rem;
}

.latest-news-item {
  background: var(--white);
  border: 2px solid var(--tc-yellow-orange);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: all 0.2s ease;
  display: flex;
  gap: 2rem;
  align-items: center;
  overflow: hidden;
}

.latest-news-item:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--tc-yellow-orange-dark);
}

.news-content {
  flex: 1;
  padding: 2rem;
}

.news-image {
  flex: 0 0 300px;
  height: 200px;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
}

.latest-news-item .news-date {
  color: var(--tc-yellow-orange);
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.latest-news-item h4 {
  color: var(--gray-900);
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  letter-spacing: -0.025em;
  line-height: 1.3;
}

.latest-news-item p {
  color: var(--gray-700);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.read-more-btn {
  background: var(--tc-yellow-orange);
  color: var(--white);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
  font-size: 0.95rem;
}

.read-more-btn:hover {
  background: var(--tc-yellow-orange-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* News Controls */
.news-controls {
  text-align: center;
  margin-bottom: 2rem;
}

.old-news-toggle {
  background: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
  padding: 0.875rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
}

.old-news-toggle:hover {
  background: var(--tc-blue);
  color: var(--white);
  border-color: var(--tc-blue);
}

.toggle-arrow {
  transition: transform 0.2s ease;
  font-size: 0.8rem;
}

.toggle-arrow.open {
  transform: rotate(180deg);
}

/* Old News Archive */
.old-news-archive {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 2px solid var(--gray-200);
}

.old-news-archive h4 {
  color: var(--gray-900);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-align: center;
}

.old-news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.old-news-item {
  background: var(--gray-50);
  padding: 1.5rem;
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
  transition: all 0.2s ease;
}

.old-news-item:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--tc-blue);
}

.old-news-item .news-date {
  color: var(--tc-blue);
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.old-news-item h5 {
  color: var(--gray-900);
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  letter-spacing: -0.025em;
  line-height: 1.3;
}

.old-news-item p {
  color: var(--gray-700);
  line-height: 1.6;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.read-more-btn-small {
  background: var(--tc-blue);
  color: var(--white);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
  font-size: 0.85rem;
}

.read-more-btn-small:hover {
  background: var(--tc-blue-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Training Page Styles */
.training-content {
  text-align: center;
  max-width: 1000px;
  margin: 0 auto;
}

.training-logo {
  max-width: 300px;
  height: auto;
  margin-bottom: 3rem;
}

/* Trainer Section */
.trainer-section {
  margin-bottom: 3rem;
}

.trainer-placeholder {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
  text-align: left;
  background: var(--gray-50);
  padding: 2rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
}

.trainer-image-placeholder {
  flex-shrink: 0;
  width: 150px;
  height: 150px;
  background: var(--gray-200);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  color: var(--gray-600);
  text-align: center;
  padding: 1rem;
  border: 2px dashed var(--gray-300);
}

.trainer-info h3 {
  color: var(--tc-yellow-orange);
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.trainer-title {
  color: var(--gray-700);
  font-weight: 500;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.trainer-description {
  color: var(--gray-700);
  line-height: 1.6;
  margin: 0;
}

/* Training Programs */
.training-programs {
  margin-bottom: 3rem;
  text-align: left;
}

.training-programs h3 {
  color: var(--gray-900);
  font-size: 1.75rem;
  margin-bottom: 2rem;
  text-align: center;
  font-weight: 600;
}

.program-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.program-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.program-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--tc-yellow-orange);
}

.program-card h4 {
  color: var(--tc-yellow-orange);
  font-size: 1.2rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.program-card ul {
  list-style: none;
  padding: 0;
  margin-bottom: 1rem;
}

.program-card li {
  color: var(--gray-700);
  margin-bottom: 0.5rem;
  padding-left: 1rem;
  position: relative;
}

.program-card li:before {
  content: "✓";
  color: var(--tc-yellow-orange);
  font-weight: bold;
  position: absolute;
  left: 0;
}

.program-time {
  color: var(--gray-800);
  font-size: 0.95rem;
  margin: 0;
  padding-top: 1rem;
  border-top: 1px solid var(--gray-200);
}

/* Training Info Section */
.training-info-section {
  margin-bottom: 3rem;
}

.pricing-contact {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  text-align: left;
}

.pricing-info,
.contact-info {
  background: var(--gray-50);
  padding: 2rem;
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
}

.pricing-info h4,
.contact-info h4 {
  color: var(--tc-yellow-orange);
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.price-list {
  list-style: none;
  padding: 0;
  margin-bottom: 1rem;
}

.price-list li {
  color: var(--gray-700);
  margin-bottom: 0.75rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-list li:last-child {
  border-bottom: none;
}

.price-note {
  color: var(--gray-600);
  font-size: 0.9rem;
  font-style: italic;
  margin: 0;
}

.contact-details p {
  color: var(--gray-700);
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.booking-note {
  background: var(--tc-yellow-orange);
  color: var(--white);
  padding: 1rem;
  border-radius: var(--radius-md);
  margin-top: 1.5rem;
}

.booking-note p {
  margin: 0;
  font-weight: 500;
  color: var(--white);
}

/* Philosophy Section */
.philosophy-section {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: 2rem;
  text-align: left;
  box-shadow: var(--shadow-sm);
}

.philosophy-section h4 {
  color: var(--tc-yellow-orange);
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  text-align: center;
  font-weight: 600;
}

.philosophy-content p {
  color: var(--gray-700);
  line-height: 1.6;
  margin-bottom: 1.25rem;
}

.philosophy-content p:last-child {
  margin-bottom: 0;
}

/* Page Headers */
.verein-content h2,
.anlage-content h2,
.kontakt-content h2,
.impressum-content h2,
.datenschutz-content h2,
.placeholder-content h2 {
  color: var(--gray-900);
  margin-bottom: 2rem;
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
}

/* Page Content */
.verein-content p,
.kontakt-info p,
.impressum-info p,
.datenschutz-info p,
.placeholder-info p {
  margin-bottom: 1rem;
  font-size: 1rem;
  line-height: 1.6;
  color: var(--gray-700);
}

/* Hero Image für Anlage */
.hero-image {
  margin-bottom: 2rem;
  text-align: center;
}

.hero-image img {
  width: 100%;
  max-width: 800px;
  height: 300px;
  object-fit: cover;
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-md);
  transition: all 0.2s ease;
}

.hero-image img:hover {
  box-shadow: var(--shadow-lg);
}

/* Strukturierte Anlage Images */
.anlage-images {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.main-images {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.secondary-images {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.main-images img,
.secondary-images img {
  width: 100%;
  height: 220px;
  object-fit: cover;
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
  transition: all 0.2s ease;
}

.main-images img:hover,
.secondary-images img:hover {
  box-shadow: var(--shadow-md);
}

/* Location Info Styles */
.location-info {
  margin: 2rem 0;
  text-align: center;
}

.location-info h3 {
  color: var(--gray-900);
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.address-card {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: 2rem;
  max-width: 400px;
  margin: 0 auto;
  box-shadow: var(--shadow-sm);
}

.address-card h4 {
  color: var(--tc-yellow-orange);
  font-size: 1.25rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.address-line {
  color: var(--gray-700);
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.address-line:last-child {
  margin-bottom: 0;
  font-weight: 500;
}

/* Google Maps Styles */
.google-maps {
  margin-top: 2rem;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

.google-maps iframe {
  width: 100%;
  height: 300px;
  border: none;
  border-radius: var(--radius-md);
}

.map-info {
  background: var(--gray-50);
  padding: 1rem;
  text-align: center;
  border-top: 1px solid var(--gray-200);
}

.map-description {
  margin: 0;
  color: var(--gray-700);
  font-size: 0.95rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Contact Info */
.kontakt-info {
  margin-bottom: 2rem;
}

.kontakt-info h3,
.impressum-info h3,
.datenschutz-info h3 {
  color: var(--tc-yellow-orange);
  margin: 1.5rem 0 1rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.datenschutz-info h4 {
  color: var(--gray-800);
  margin: 1rem 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 500;
}

.datenschutz-info ul {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.datenschutz-info li {
  margin-bottom: 0.5rem;
  color: var(--gray-700);
}

/* Placeholder Page */
.placeholder-info {
  text-align: center;
  font-size: 1rem;
  line-height: 1.6;
}

/* Clean Back Button Styles */
.back-button-container {
  margin-bottom: 2rem;
  text-align: left;
}

.back-button {
  background: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.back-button:hover {
  background: var(--gray-200);
  color: var(--gray-900);
}

/* Clean Footer Styles */
.footer {
  background: var(--gray-100);
  color: var(--gray-700);
  padding: 2rem 0;
  margin-top: auto;
  border-top: 1px solid var(--gray-200);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.footer-links {
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.footer-links a {
  color: var(--gray-600);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  padding: 0.25rem 0.5rem;
  transition: color 0.2s ease;
}

.footer-links a:hover {
  color: var(--gray-900);
}

.footer-links span {
  color: var(--gray-400);
  font-weight: 300;
}

.footer-text p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--gray-500);
  font-weight: 400;
}

/* Clean Mobile Floating Action Button */
.floating-action-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 60px;
  height: 60px;
  background: var(--tc-yellow-orange);
  border: none;
  border-radius: 50%;
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  color: var(--white);
  transition: all 0.2s ease;
}

.floating-action-btn:hover {
  background: var(--tc-yellow-orange-dark);
  box-shadow: var(--shadow-xl);
}

.fab-text {
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Hide FAB on desktop - only show on mobile/tablet */
@media (min-width: 1024px) {
  .floating-action-btn {
    display: none;
  }
}

/* Adjust FAB size for smaller screens */
@media (max-width: 480px) {
  .floating-action-btn {
    width: 56px;
    height: 56px;
    bottom: 1.5rem;
    right: 1.5rem;
  }

  .fab-text {
    font-size: 0.65rem;
  }
}

/* Touch and Mobile Improvements */
* {
  -webkit-tap-highlight-color: transparent;
}

/* Teaser Sections Styles */
.teasers-section {
  background: var(--white);
  padding: 3rem 2rem;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  border-top: 3px solid var(--tc-blue);
}

.teasers-section h3 {
  text-align: center;
  color: var(--gray-900);
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 3rem;
}

.teasers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.teaser-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.teaser-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--tc-yellow-orange);
  transform: translateY(-2px);
}

.teaser-highlight {
  border: 2px solid var(--tc-yellow-orange);
  background: linear-gradient(135deg, var(--white) 0%, rgba(255, 193, 7, 0.05) 100%);
}

.teaser-highlight:hover {
  border-color: var(--tc-yellow-orange-dark);
  box-shadow: 0 12px 30px rgba(255, 193, 7, 0.2);
}

.teaser-image {
  height: 180px;
  overflow: hidden;
  position: relative;
}

.teaser-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.teaser-card:hover .teaser-image img {
  transform: scale(1.05);
}

.teaser-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  flex: 1;
  text-align: center;
}

.teaser-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.teaser-card h4 {
  color: var(--gray-900);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.teaser-card p {
  color: var(--gray-600);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex: 1;
}

.teaser-button {
  background: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
}

.teaser-button:hover {
  background: var(--tc-yellow-orange);
  color: var(--white);
  border-color: var(--tc-yellow-orange);
}

.teaser-button-primary {
  background: var(--tc-yellow-orange);
  color: var(--white);
  border-color: var(--tc-yellow-orange);
}

.teaser-button-primary:hover {
  background: var(--tc-yellow-orange-dark);
  border-color: var(--tc-yellow-orange-dark);
}

/* Improved Touch Targets for Mobile */
@media (max-width: 768px) {
  .nav-list a {
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .booking-button {
    padding: 1rem 2rem !important;
    font-size: 1.1rem !important;
    min-height: 52px !important;
  }

  .mobile-menu-toggle {
    min-width: 48px;
    min-height: 48px;
  }

  /* Better mobile content spacing */
  .content {
    padding: 2rem 1.5rem;
    margin: 0.5rem;
  }

  .welcome-message {
    padding: 2.5rem 2rem;
  }

  .news-section {
    padding: 2rem 1.5rem;
  }

  .news-item {
    padding: 1.5rem;
  }

  /* Teaser mobile styles */
  .teasers-section {
    padding: 2rem 1.5rem;
  }

  .teasers-section h3 {
    font-size: 1.75rem;
    margin-bottom: 2rem;
  }

  .teasers-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .teaser-card {
    padding: 1.5rem;
  }

  .teaser-icon {
    font-size: 2.5rem;
  }

  .teaser-button {
    padding: 1rem 1.5rem;
    font-size: 1rem;
    min-height: 48px;
  }

  /* Improved mobile forms */
  .form-input {
    font-size: 16px; /* Prevents zoom on iOS */
    min-height: 48px;
  }

  /* Better mobile buttons */
  .btn {
    min-height: 48px;
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }

  .back-button {
    min-height: 48px;
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .header-content {
    padding: 0 1rem;
  }

  .logo h1 {
    font-size: 1.8rem;
  }

  .welcome-message h2 {
    font-size: 2.5rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  .content {
    padding: 1.5rem 1rem;
    margin: 0.25rem;
  }

  .news-grid {
    gap: 1rem;
  }

  .news-item {
    padding: 1.25rem;
  }
}

/* Landscape mobile optimization */
@media (max-height: 500px) and (orientation: landscape) {
  .welcome-message {
    padding: 1.5rem;
  }

  .welcome-message h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .news-section {
    padding: 1.5rem;
  }

  .floating-action-btn {
    width: 52px;
    height: 52px;
    bottom: 1rem;
    right: 1rem;
  }
}

/* Clean Mobile Optimizations */
@media (max-width: 768px) {
  /* Add padding-top to body to account for fixed header */
  body {
    padding-top: 70px;
  }

  .header-content {
    justify-content: space-between;
    padding: 0.75rem 1rem;
  }

  .logo {
    gap: 0.75rem;
  }

  .logo-img {
    height: 40px;
    width: 40px;
  }

  .logo h1 {
    font-size: 1.5rem;
  }

  /* Hide desktop navigation on mobile */
  .desktop-nav {
    display: none !important;
  }

  /* Show hamburger menu on mobile */
  .mobile-menu-toggle {
    display: flex !important;
  }

  /* Clean mobile navigation */
  .mobile-nav {
    display: block;
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--white);
    border-top: 1px solid var(--gray-200);
    transform: translateY(-100%);
    transition: transform 0.3s ease;
    box-shadow: var(--shadow-lg);
    max-height: calc(100vh - 70px);
    overflow-y: auto;
  }

  /* Show navigation when open */
  .mobile-nav.nav-open {
    transform: translateY(0);
  }

  .nav-list {
    flex-direction: column;
    gap: 0;
    padding: 1rem 0;
    margin: 0;
    width: 100%;
  }

  .nav-list li {
    width: 100%;
    border-bottom: 1px solid var(--gray-100);
  }

  .nav-list li:last-child {
    border-bottom: none;
  }

  .nav-list a {
    display: block;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    width: 100%;
    text-align: left;
    border-radius: 0;
    color: var(--gray-700);
    transition: background-color 0.2s ease;
  }

  .nav-list a:hover,
  .nav-list a.active {
    background-color: var(--gray-50);
    color: var(--gray-900);
  }

  .booking-button {
    background: var(--tc-yellow-orange) !important;
    color: var(--white) !important;
    font-weight: 600 !important;
  }

  .booking-button:hover {
    background: var(--tc-yellow-orange-dark) !important;
  }



  /* Clean mobile dropdown */
  .dropdown-menu {
    position: static;
    opacity: 0;
    visibility: hidden;
    max-height: 0;
    overflow: hidden;
    transform: none;
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    margin: 0.5rem 0;
    transition: all 0.2s ease;
  }

  /* Show dropdown on click (mobile) */
  .dropdown-open .dropdown-menu {
    opacity: 1;
    visibility: visible;
    max-height: 300px;
  }

  .dropdown-menu a {
    padding: 0.75rem 1.5rem !important;
    padding-left: 2rem !important;
    font-size: 0.95rem !important;
    color: var(--gray-700) !important;
    font-weight: 500 !important;
    text-align: left !important;
    border-bottom: 1px solid var(--gray-200);
    transition: all 0.2s ease !important;
  }

  .dropdown-menu a:last-child {
    border-bottom: none;
  }

  .dropdown-menu a:hover {
    background-color: var(--gray-100) !important;
    color: var(--gray-900) !important;
  }

  /* Mobile Home Page */
  .home-content {
    padding: 1rem;
    gap: 1.5rem;
  }

  .welcome-message {
    padding: 2rem 1.5rem;
  }

  .welcome-message h2 {
    font-size: 2rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  .news-section {
    padding: 1.5rem;
  }

  .news-section h3 {
    font-size: 1.6rem;
    margin-bottom: 1rem;
  }

  .news-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .news-item {
    padding: 1.2rem;
  }

  .news-item h4 {
    font-size: 1.1rem;
  }

  /* Mobile Latest News Styles */
  .latest-news-item {
    padding: 1.5rem;
  }

  .latest-news-item h4 {
    font-size: 1.3rem;
    margin-bottom: 0.75rem;
  }

  .latest-news-item p {
    font-size: 0.95rem;
    margin-bottom: 1rem;
  }

  .read-more-btn {
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
    width: 100%;
    justify-content: center;
  }

  /* Mobile News Controls */
  .news-controls {
    margin-bottom: 1.5rem;
  }

  .old-news-toggle {
    padding: 1rem 1.5rem;
    font-size: 0.9rem;
    width: 100%;
    justify-content: center;
  }

  /* Mobile Old News Archive */
  .old-news-archive {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
  }

  .old-news-archive h4 {
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }

  .old-news-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .old-news-item {
    padding: 1.25rem;
  }

  .old-news-item h5 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  .old-news-item p {
    font-size: 0.85rem;
    margin-bottom: 0.75rem;
  }

  .read-more-btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    width: 100%;
    justify-content: center;
  }

  .content {
    padding: 1rem;
    margin: 0.5rem;
    border-radius: 15px;
  }

  /* Mobile Hero Image */
  .hero-image img {
    height: 200px;
    max-width: 100%;
  }

  /* Mobile Anlage Images */
  .main-images,
  .secondary-images {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .main-images img,
  .secondary-images img {
    height: 180px;
  }

  /* Mobile Location Info */
  .location-info {
    margin: 1.5rem 0;
  }

  .location-info h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
  }

  .address-card {
    padding: 1.5rem;
    max-width: 100%;
    margin: 0;
  }

  .address-card h4 {
    font-size: 1.1rem;
  }

  .address-line {
    font-size: 1rem;
  }

  /* Mobile Google Maps */
  .google-maps {
    margin-top: 1.5rem;
    border-radius: var(--radius-md);
  }

  .google-maps iframe {
    height: 250px;
  }

  .map-info {
    padding: 0.75rem;
  }

  .map-description {
    font-size: 0.9rem;
    flex-direction: column;
    gap: 0.25rem;
  }



  /* Mobile Footer */
  .footer-content {
    padding: 0 1rem;
  }

  .footer-links {
    gap: 0.5rem;
    font-size: 0.9rem;
  }

  .footer-text p {
    font-size: 0.8rem;
  }

  /* Mobile page optimization */
  .page {
    background-attachment: scroll;
    padding: 2rem 0;
  }

  .page::before {
    background: rgba(255, 255, 255, 0.95);
  }

  /* Mobile back button */
  .back-button {
    font-size: 0.9rem;
    padding: 0.7rem 1.2rem;
    width: 100%;
    justify-content: center;
    margin-bottom: 1rem;
  }

  /* Mobile Training Page */
  .training-content {
    padding: 0;
  }

  .training-logo {
    max-width: 250px;
    margin-bottom: 2rem;
  }

  .trainer-placeholder {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .trainer-image-placeholder {
    width: 120px;
    height: 120px;
    margin: 0 auto;
  }

  .program-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .program-card {
    padding: 1.25rem;
  }

  .program-card h4 {
    font-size: 1.1rem;
  }

  .pricing-contact {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .pricing-info,
  .contact-info {
    padding: 1.5rem;
  }

  .price-list li {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .philosophy-section {
    padding: 1.5rem;
  }
}

/* Verein Page Styles */
.verein-section {
  margin-bottom: 3rem;
}

.verein-section h3 {
  color: var(--gray-900);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid var(--tc-yellow-orange);
  padding-bottom: 0.5rem;
}

.verein-highlights {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.highlight-item {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  text-align: center;
}

.highlight-item h4 {
  color: var(--gray-900);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.highlight-item p {
  color: var(--gray-600);
  font-size: 0.95rem;
  line-height: 1.5;
}

.membership-benefits {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  margin: 1.5rem 0;
}

.membership-benefits li {
  margin-bottom: 0.5rem;
  color: var(--gray-700);
}

/* Kontakt Page Styles */
.contact-section {
  margin-bottom: 3rem;
}

.contact-section h3 {
  color: var(--gray-900);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid var(--tc-blue);
  padding-bottom: 0.5rem;
}

.contact-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.contact-card {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  text-align: center;
}

.contact-card h4 {
  color: var(--gray-900);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.contact-persons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.person-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.person-card h4 {
  color: var(--gray-900);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.person-card p {
  margin-bottom: 0.5rem;
  color: var(--gray-700);
}

.person-card em {
  color: var(--gray-500);
  font-size: 0.9rem;
}

.opening-hours {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.hours-card {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: 1.5rem;
}

.hours-card h4 {
  color: var(--gray-900);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.hours-card ul {
  margin-bottom: 1rem;
}

.hours-card li {
  margin-bottom: 0.5rem;
  color: var(--gray-700);
}

.directions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.direction-item {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.direction-item h4 {
  color: var(--gray-900);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.contact-note {
  background: var(--white);
  color: var(--gray-900);
  border-radius: var(--radius-md);
  padding: 2rem;
  text-align: center;
  margin: 2rem 0;
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

.contact-note h4 {
  color: var(--gray-900);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.contact-note p {
  color: var(--gray-700);
  line-height: 1.6;
}

@media (max-width: 480px) {
  .logo h1 {
    font-size: 1.25rem;
  }

  .welcome-message {
    padding: 2rem 1rem;
  }

  .welcome-message h2 {
    font-size: 2rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  /* Mobile adjustments for new content */
  .verein-highlights,
  .contact-cards,
  .contact-persons,
  .opening-hours,
  .directions {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .contact-card,
  .person-card,
  .hours-card,
  .direction-item {
    padding: 1.25rem;
  }

  .contact-note {
    padding: 1.5rem;
  }

  /* Mobile Hero Section */
  .hero-section {
    min-height: 50vh;
  }

  .hero-title {
    font-size: 2.5rem;
    text-shadow: none;
    color: #000000 !important;
  }

  .hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    text-shadow: none;
    color: #000000 !important;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .hero-button {
    width: 100%;
    max-width: 280px;
  }

  /* Mobile News Section with Images */
  .latest-news-item {
    flex-direction: column;
    gap: 0;
  }

  .news-content {
    padding: 1.5rem;
  }

  .news-image {
    flex: none;
    height: 200px;
  }

  .news-image img {
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  }

  /* Mobile Teaser Cards with Images */
  .teaser-content {
    padding: 1.5rem;
  }

  .teaser-image {
    height: 160px;
  }
}

/* TC-Sandanger Inspired Page Styles */

/* Page Container - Clean Layout */
.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* Hero Section - Minimalist */
.page-hero {
  padding: 4rem 0 3rem;
  text-align: center;
  border-bottom: 1px solid var(--gray-100);
  margin-bottom: 3rem;
}

.page-hero .hero-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.page-hero .hero-content p {
  font-size: 1.125rem;
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Training Page Styles */
.training-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.category-card {
  background: var(--white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
}

.category-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.category-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.category-card:hover .category-image img {
  transform: scale(1.05);
}

.category-content {
  padding: 1.5rem;
}

.category-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 0.75rem;
}

.category-content p {
  color: var(--gray-600);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.category-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-features li {
  color: var(--gray-700);
  padding: 0.25rem 0;
  position: relative;
  padding-left: 1rem;
}

.category-features li::before {
  content: '•';
  color: var(--tc-yellow-orange);
  position: absolute;
  left: 0;
  font-weight: bold;
}

/* Trainer Section */
.trainer-section {
  background: var(--gray-50);
  padding: 3rem 2rem;
  border-radius: var(--radius-lg);
  margin-bottom: 3rem;
}

.trainer-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 3rem;
  align-items: center;
}

.trainer-info .training-logo {
  max-width: 200px;
  margin-bottom: 1rem;
}

.trainer-info h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 0.5rem;
}

.trainer-title {
  color: var(--tc-yellow-orange);
  font-weight: 600;
  margin-bottom: 1rem;
}

.trainer-description {
  color: var(--gray-700);
  line-height: 1.6;
}

.trainer-image {
  display: flex;
  justify-content: center;
}

.trainer-placeholder {
  width: 250px;
  height: 250px;
  background: var(--gray-200);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-500);
  font-style: italic;
}

/* Contact & Pricing Grid */
.training-contact-section {
  margin-bottom: 3rem;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.contact-card,
.pricing-card {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.contact-card h4,
.pricing-card h4 {
  color: var(--gray-900);
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.contact-details p {
  margin-bottom: 0.5rem;
  color: var(--gray-700);
}

.booking-note {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--gray-50);
  border-radius: var(--radius-md);
  color: var(--gray-700);
  font-style: italic;
}

.price-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.price-list li {
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--gray-100);
  display: flex;
  justify-content: space-between;
  color: var(--gray-700);
}

.price-list li:last-child {
  border-bottom: none;
}

/* Verein Page Styles */
.club-gallery {
  margin-bottom: 4rem;
}

.gallery-main {
  margin-bottom: 1rem;
}

.gallery-main img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: var(--radius-lg);
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.gallery-grid img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: var(--radius-md);
}

.club-info-section {
  margin-bottom: 4rem;
}

.club-story {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.club-story h3 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 1.5rem;
}

.club-story p {
  color: var(--gray-700);
  line-height: 1.6;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

/* Club Highlights */
.club-highlights {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.highlight-card {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
}

.highlight-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.highlight-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.highlight-card h4 {
  color: var(--gray-900);
  margin-bottom: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.highlight-card p {
  color: var(--gray-600);
  line-height: 1.6;
}

/* Board Section */
.board-section {
  margin-bottom: 4rem;
}

.board-section h3 {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 2rem;
}

.board-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.board-member {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
}

.board-member:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.member-photo-placeholder {
  width: 120px;
  height: 120px;
  background: var(--gray-200);
  border-radius: 50%;
  margin: 0 auto 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-500);
  font-style: italic;
}

.board-member h4 {
  color: var(--tc-yellow-orange);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.member-name {
  color: var(--gray-900);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.member-role {
  color: var(--gray-600);
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Membership Section */
.membership-section {
  background: var(--gray-50);
  padding: 3rem 2rem;
  border-radius: var(--radius-lg);
  margin-bottom: 4rem;
}

.membership-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 3rem;
  align-items: center;
}

.membership-info h3 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 1rem;
}

.membership-info p {
  color: var(--gray-700);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.membership-benefits {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.membership-benefits li {
  color: var(--gray-700);
  padding: 0.5rem 0;
  position: relative;
  padding-left: 1.5rem;
}

.membership-benefits li::before {
  content: '✓';
  color: var(--tc-yellow-orange);
  position: absolute;
  left: 0;
  font-weight: bold;
}

.membership-note {
  background: var(--white);
  padding: 1rem;
  border-radius: var(--radius-md);
  border-left: 4px solid var(--tc-yellow-orange);
  margin-top: 1rem;
}

.membership-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: var(--radius-lg);
}

/* Documents Section */
.documents-section {
  margin-bottom: 4rem;
}

.documents-section h3 {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 2rem;
}

.documents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.document-card {
  background: var(--white);
  padding: 2rem 1.5rem;
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
  cursor: pointer;
}

.document-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  border-color: var(--tc-yellow-orange);
}

.document-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.document-card h4 {
  color: var(--gray-900);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.document-card p {
  color: var(--gray-600);
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Anlage Page Styles */
.facility-hero-image {
  margin-bottom: 3rem;
  position: relative;
}

.facility-hero-image img {
  width: 100%;
  height: 500px;
  object-fit: cover;
  border-radius: var(--radius-lg);
}

.image-caption {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  text-align: center;
}

.image-caption p {
  margin: 0;
  font-size: 0.9rem;
}

/* Facility Features */
.facility-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.feature-card {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
}

.feature-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  color: var(--gray-900);
  margin-bottom: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.feature-card p {
  color: var(--gray-600);
  line-height: 1.6;
}

/* Facility Gallery */
.facility-gallery {
  margin-bottom: 4rem;
}

.facility-gallery h3 {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 2rem;
}

.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-lg);
  transition: transform 0.3s ease;
}

.gallery-item:hover {
  transform: scale(1.02);
}

.gallery-item img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.gallery-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.75rem;
  text-align: center;
  font-size: 0.9rem;
}

/* Facility Description */
.facility-description {
  background: var(--gray-50);
  padding: 3rem 2rem;
  border-radius: var(--radius-lg);
  margin-bottom: 4rem;
  text-align: center;
}

.description-content h3 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 1.5rem;
}

.description-content p {
  color: var(--gray-700);
  line-height: 1.6;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* Location Section */
.location-section {
  margin-bottom: 4rem;
}

.location-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

.location-info h3 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 1.5rem;
}

.address-card {
  background: var(--white);
  padding: 1.5rem;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  margin-bottom: 2rem;
}

.address-card h4 {
  color: var(--tc-yellow-orange);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.address-line {
  color: var(--gray-700);
  margin-bottom: 0.25rem;
}

.directions h4 {
  color: var(--gray-900);
  margin-bottom: 0.75rem;
  margin-top: 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.directions p {
  color: var(--gray-700);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.location-map iframe {
  width: 100%;
  height: 400px;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

/* Kontakt Page Styles */
.main-contact-section {
  margin-bottom: 4rem;
}

.contact-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.contact-info-card {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
}

.contact-info-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.contact-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.contact-info-card h3 {
  color: var(--gray-900);
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.contact-detail {
  color: var(--gray-900);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.contact-note {
  color: var(--gray-600);
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Contact Persons */
.contact-persons-section {
  margin-bottom: 4rem;
}

.contact-persons-section h3 {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 2rem;
}

.persons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.person-card {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
  display: flex;
  gap: 1.5rem;
  align-items: start;
}

.person-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.person-photo-placeholder {
  width: 80px;
  height: 80px;
  background: var(--gray-200);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-500);
  font-style: italic;
  flex-shrink: 0;
}

.person-icon {
  font-size: 2rem;
}

.person-info {
  flex: 1;
}

.person-info h4 {
  color: var(--tc-yellow-orange);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.person-name {
  color: var(--gray-900);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.person-contact {
  color: var(--gray-700);
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.person-role {
  color: var(--gray-600);
  font-size: 0.9rem;
  line-height: 1.4;
  font-style: italic;
}

/* Opening Hours */
.opening-hours-section {
  margin-bottom: 4rem;
}

.opening-hours-section h3 {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 2rem;
}

.hours-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.hours-card {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
}

.hours-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.season-icon {
  font-size: 3rem;
  text-align: center;
  margin-bottom: 1rem;
}

.hours-card h4 {
  color: var(--gray-900);
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  text-align: center;
}

.hours-list {
  margin-bottom: 1rem;
}

.hours-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--gray-100);
}

.hours-item:last-child {
  border-bottom: none;
}

.day {
  color: var(--gray-700);
  font-weight: 500;
}

.time {
  color: var(--gray-900);
  font-weight: 600;
}

.hours-note {
  color: var(--gray-600);
  font-size: 0.9rem;
  line-height: 1.4;
  font-style: italic;
  text-align: center;
  margin: 0;
}

/* Directions */
.directions-section {
  margin-bottom: 4rem;
}

.directions-section h3 {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 2rem;
}

.directions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.direction-card {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
}

.direction-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.direction-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.direction-card h4 {
  color: var(--gray-900);
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.direction-card p {
  color: var(--gray-700);
  line-height: 1.6;
  text-align: left;
}

/* Contact Note Section */
.contact-note-section {
  background: var(--gray-50);
  padding: 3rem 2rem;
  border-radius: var(--radius-lg);
  text-align: center;
  margin-bottom: 4rem;
}

.note-content {
  max-width: 600px;
  margin: 0 auto;
}

.note-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.note-content h3 {
  color: var(--gray-900);
  margin-bottom: 1rem;
  font-size: 1.75rem;
  font-weight: 600;
}

.note-content p {
  color: var(--gray-700);
  line-height: 1.6;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

/* Legal Pages Styles */
.legal-content {
  max-width: 800px;
  margin: 0 auto;
}

.legal-section {
  margin-bottom: 3rem;
}

.legal-section h3 {
  color: var(--gray-900);
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.legal-info {
  background: var(--white);
  padding: 1.5rem;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.legal-info p {
  color: var(--gray-700);
  line-height: 1.6;
  margin-bottom: 0.5rem;
}

.legal-info p:last-child {
  margin-bottom: 0;
}

/* Privacy Content */
.privacy-content {
  max-width: 800px;
  margin: 0 auto;
}

.privacy-section {
  margin-bottom: 3rem;
}

.privacy-section h3 {
  color: var(--gray-900);
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.privacy-section h4 {
  color: var(--gray-800);
  margin-bottom: 0.75rem;
  margin-top: 1.5rem;
  font-size: 1.2rem;
  font-weight: 600;
}

.privacy-section p {
  color: var(--gray-700);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.privacy-list {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.privacy-list li {
  color: var(--gray-700);
  padding: 0.25rem 0;
  position: relative;
  padding-left: 1rem;
}

.privacy-list li::before {
  content: '•';
  color: var(--tc-yellow-orange);
  position: absolute;
  left: 0;
  font-weight: bold;
}

/* Placeholder Page */
.placeholder-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.placeholder-card {
  background: var(--white);
  padding: 3rem 2rem;
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  max-width: 500px;
}

.placeholder-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.placeholder-card h3 {
  color: var(--gray-900);
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.placeholder-card p {
  color: var(--gray-700);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.contact-link {
  color: var(--tc-yellow-orange);
  text-decoration: underline;
  font-weight: 500;
}

.contact-link:hover {
  color: var(--tc-yellow-orange-dark);
}

/* Mobile Responsive Styles for New Pages */
@media (max-width: 768px) {
  /* Page Hero Mobile */
  .page-hero {
    padding: 2rem 0 1.5rem;
    margin-bottom: 2rem;
  }

  .page-hero .hero-content h1 {
    font-size: 2rem;
  }

  .page-hero .hero-content p {
    font-size: 1rem;
  }

  /* Training Categories Mobile */
  .training-categories {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 3rem;
  }

  .trainer-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  /* Verein Page Mobile */
  .gallery-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .club-highlights {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .board-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .membership-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .documents-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  /* Anlage Page Mobile */
  .facility-features {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .facility-hero-image img {
    height: 300px;
  }

  .location-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .location-map iframe {
    height: 300px;
  }

  /* Kontakt Page Mobile */
  .contact-info-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .persons-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .person-card {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .hours-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .directions-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  /* General Mobile Adjustments */
  .page-container {
    padding: 0 1rem;
  }

  .trainer-section,
  .membership-section,
  .facility-description,
  .contact-note-section {
    padding: 2rem 1rem;
  }

  .club-story h3,
  .board-section h3,
  .documents-section h3,
  .facility-gallery h3,
  .contact-persons-section h3,
  .opening-hours-section h3,
  .directions-section h3 {
    font-size: 1.5rem;
  }

  .category-card,
  .highlight-card,
  .board-member,
  .document-card,
  .feature-card,
  .contact-info-card,
  .person-card,
  .hours-card,
  .direction-card {
    padding: 1.5rem;
  }

  .note-content h3 {
    font-size: 1.5rem;
  }

  .note-content p {
    font-size: 1rem;
  }

  .legal-content,
  .privacy-content {
    padding: 0 1rem;
  }
}
