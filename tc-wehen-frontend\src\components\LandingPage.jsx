import React, { useState, useEffect } from 'react';
import './LandingPage.css';

const LandingPage = ({ onBookingClick }) => {
  const [currentPage, setCurrentPage] = useState('home');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isHeaderVisible, setIsHeaderVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [showOldNews, setShowOldNews] = useState(false);

  // News data structure
  const newsData = [
    {
      id: 1,
      date: '15. Dez 2024',
      title: 'Winterpause beendet',
      excerpt: 'Ab sofort sind unsere Plätze wieder geöffnet! Wir freuen uns auf die neue Saison.',
      content: 'Ab sofort sind unsere Plätze wieder geöffnet! Wir freuen uns auf die neue Saison und heißen alle Mitglieder herzlich willkommen. Die Plätze sind in einem hervorragenden Zustand und bereit für spannende Matches.',
      isLatest: true
    },
    {
      id: 2,
      date: '10. Dez 2024',
      title: 'Neue Buchungszeiten',
      excerpt: 'Das Online-Buchungssystem ist jetzt verfügbar. Buchen Sie Ihren Platz bequem von zu Hause.',
      content: 'Das Online-Buchungssystem ist jetzt verfügbar. Buchen Sie Ihren Platz bequem von zu Hause aus. Das System ist benutzerfreundlich und ermöglicht es, Plätze bis zu 7 Tage im Voraus zu reservieren.',
      isLatest: false
    },
    {
      id: 3,
      date: '5. Dez 2024',
      title: 'Tennisschule Prätorius',
      excerpt: 'Professionelles Training für alle Altersgruppen. Kontaktieren Sie Frank Prätorius für weitere Informationen.',
      content: 'Professionelles Training für alle Altersgruppen. Kontaktieren Sie Frank Prätorius für weitere Informationen. Neue Kurse starten im Januar 2025 - sowohl für Anfänger als auch für Fortgeschrittene.',
      isLatest: false
    },
    {
      id: 4,
      date: '1. Dez 2024',
      title: 'Vereinsfeier 2024',
      excerpt: 'Rückblick auf eine erfolgreiche Saison mit allen Mitgliedern.',
      content: 'Rückblick auf eine erfolgreiche Saison mit allen Mitgliedern. Bei der diesjährigen Vereinsfeier haben wir die Erfolge unserer Mannschaften gefeiert und das Jahr gemeinsam ausklingen lassen.',
      isLatest: false
    },
    {
      id: 5,
      date: '25. Nov 2024',
      title: 'Platzpflege abgeschlossen',
      excerpt: 'Die jährliche Winterpflege unserer Sandplätze wurde erfolgreich durchgeführt.',
      content: 'Die jährliche Winterpflege unserer Sandplätze wurde erfolgreich durchgeführt. Alle sechs Plätze sind nun optimal für die kommende Saison vorbereitet.',
      isLatest: false
    }
  ];

  const latestNews = newsData.find(news => news.isLatest);
  const oldNews = newsData.filter(news => !news.isLatest);

  // Scroll-Handler für Auto-Hide/Show Header
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        // Scrolling down & past 100px
        setIsHeaderVisible(false);
        setIsMobileMenuOpen(false); // Close mobile menu when scrolling down
      } else if (currentScrollY < lastScrollY) {
        // Scrolling up
        setIsHeaderVisible(true);
      }

      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  const navigate = (page) => {
    // Handle external redirects
    if (page === 'medenspiele') {
      window.open('https://htv.liga.nu/cgi-bin/WebObjects/nuLigaTENDE.woa/wa/clubMeetings?club=24972', '_blank');
      return;
    }
    
    if (page === 'mitglied-werden') {
      window.open('http://tc-wehen.de/wp-content/uploads/2025/04/Beitragsordnung_TCWehen_2025.pdf', '_blank');
      return;
    }
    
    if (page === 'satzung') {
      window.open('https://tc-wehen.com/wp-content/uploads/2021/03/Satzung_TC-Wehen_Maerz_2020.pdf', '_blank');
      return;
    }
    
    if (page === 'hand-spanndienst') {
      window.open('http://tc-wehen.de/wp-content/uploads/2024/04/TCW_HSD_Arbeitsstunden.pdf', '_blank');
      return;
    }
    
    if (page === 'mach-mit') {
      window.open('http://tc-wehen.de/wp-content/uploads/2024/01/Mithilfe_Mitglieder.pdf', '_blank');
      return;
    }

    if (page === 'buchung') {
      onBookingClick();
      return;
    }

    setCurrentPage(page);
    setIsMobileMenuOpen(false); // Close mobile menu when navigating
    setIsDropdownOpen(false); // Close dropdown when navigating
  };

  const toggleDropdown = (e) => {
    e.preventDefault();
    setIsDropdownOpen(!isDropdownOpen);
  };

  const Header = () => (
    <header className={`header ${isHeaderVisible ? 'header-visible' : 'header-hidden'}`}>
      <div className="header-content">
        <div className="logo">
          <img src="/assets/Logo-TCW.PNG" alt="TC-Wehen Logo" className="logo-img" />
          <h1>TC-Wehen</h1>
        </div>

        {/* Desktop Navigation */}
        <nav className="nav desktop-nav">
          <ul className="nav-list">
            <li><a href="#" onClick={() => navigate('home')} className={currentPage === 'home' ? 'active' : ''}>Home</a></li>
            <li><a href="#" onClick={() => navigate('training')} className={currentPage === 'training' ? 'active' : ''}>Training</a></li>
            <li><a href="#" onClick={() => navigate('buchung')} className="booking-button">Platz buchen</a></li>
            <li className={`dropdown ${isDropdownOpen ? 'dropdown-open' : ''}`}>
              <a href="#" onClick={toggleDropdown} className={currentPage === 'verein' ? 'active' : ''}>
                Verein <span className="dropdown-arrow">▼</span>
              </a>
              <ul className="dropdown-menu">
                <li><a href="#" onClick={() => navigate('verein')}>Verein Info</a></li>
                <li><a href="#" onClick={() => navigate('verein-anlage')}>Unsere Anlage</a></li>
                <li><a href="#" onClick={() => navigate('mitglied-werden')}>Mitglied werden</a></li>
                <li><a href="#" onClick={() => navigate('satzung')}>Satzung</a></li>
                <li><a href="#" onClick={() => navigate('hand-spanndienst')}>Hand- und Spanndienst</a></li>
                <li><a href="#" onClick={() => navigate('mach-mit')}>Mach Mit!</a></li>
              </ul>
            </li>
            <li><a href="#" onClick={() => navigate('medenspiele')} className={currentPage === 'medenspiele' ? 'active' : ''}>Medenspiele</a></li>
            <li><a href="#" onClick={() => navigate('kontakt')} className={currentPage === 'kontakt' ? 'active' : ''}>Kontakt</a></li>
          </ul>
        </nav>

        {/* Hamburger Menu Button - Only visible on mobile */}
        <button
          className="mobile-menu-toggle"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Menu"
        >
          <span className={`hamburger-line ${isMobileMenuOpen ? 'open' : ''}`}></span>
          <span className={`hamburger-line ${isMobileMenuOpen ? 'open' : ''}`}></span>
          <span className={`hamburger-line ${isMobileMenuOpen ? 'open' : ''}`}></span>
        </button>

        {/* Mobile Navigation - Only visible when menu is open */}
        <nav className={`nav mobile-nav ${isMobileMenuOpen ? 'nav-open' : ''}`}>
          <ul className="nav-list">
            <li><a href="#" onClick={() => navigate('home')} className={currentPage === 'home' ? 'active' : ''}>Home</a></li>
            <li><a href="#" onClick={() => navigate('training')} className={currentPage === 'training' ? 'active' : ''}>Training</a></li>
            <li><a href="#" onClick={() => navigate('buchung')} className="booking-button">Platz buchen</a></li>
            <li className={`dropdown ${isDropdownOpen ? 'dropdown-open' : ''}`}>
              <a href="#" onClick={toggleDropdown} className={currentPage === 'verein' ? 'active' : ''}>
                Verein <span className="dropdown-arrow">▼</span>
              </a>
              <ul className="dropdown-menu">
                <li><a href="#" onClick={() => navigate('verein')}>Verein Info</a></li>
                <li><a href="#" onClick={() => navigate('verein-anlage')}>Unsere Anlage</a></li>
                <li><a href="#" onClick={() => navigate('mitglied-werden')}>Mitglied werden</a></li>
                <li><a href="#" onClick={() => navigate('satzung')}>Satzung</a></li>
                <li><a href="#" onClick={() => navigate('hand-spanndienst')}>Hand- und Spanndienst</a></li>
                <li><a href="#" onClick={() => navigate('mach-mit')}>Mach Mit!</a></li>
              </ul>
            </li>
            <li><a href="#" onClick={() => navigate('medenspiele')} className={currentPage === 'medenspiele' ? 'active' : ''}>Medenspiele</a></li>
            <li><a href="#" onClick={() => navigate('kontakt')} className={currentPage === 'kontakt' ? 'active' : ''}>Kontakt</a></li>
          </ul>
        </nav>
      </div>
    </header>
  );

  const HomePage = () => (
    <div className="page home-page">
      {/* Hero Section with Background Image */}
      <div className="hero-section" style={{backgroundImage: "url('/assets/Obenansicht-Plätze.PNG')"}}>
        <div className="hero-overlay">
          <div className="hero-content">
            <h1 className="hero-title">Ihr Tennisverein in Wehen</h1>
            <p className="hero-subtitle">Unser Tennisclub liegt herrlich in Wehen mit 6 gepflegten Sandplätzen und einem gemütlichen Vereinsheim.</p>
            <div className="hero-actions">
              <button className="hero-button primary" onClick={() => navigate('buchung')}>
                Platz buchen
              </button>
              <button className="hero-button secondary" onClick={() => navigate('verein')}>
                Verein kennenlernen
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="home-content">

        <div className="news-section">
          <h3>Aktuelle News</h3>

          {/* Latest News Display */}
          {latestNews && (
            <div className="latest-news">
              <div className="latest-news-item">
                <div className="news-content">
                  <div className="news-date">{latestNews.date}</div>
                  <h4>{latestNews.title}</h4>
                  <p>{latestNews.excerpt}</p>
                  {latestNews.content !== latestNews.excerpt && (
                    <button className="read-more-btn">
                      Weiterlesen →
                    </button>
                  )}
                </div>
                <div className="news-image">
                  <img src="/assets/Plätze.PNG" alt="TC-Wehen Tennisplätze" />
                </div>
              </div>
            </div>
          )}

          {/* Old News Toggle Button */}
          <div className="news-controls">
            <button
              className="old-news-toggle"
              onClick={() => setShowOldNews(!showOldNews)}
            >
              {showOldNews ? 'Alte News ausblenden' : 'Alte News anzeigen'}
              <span className={`toggle-arrow ${showOldNews ? 'open' : ''}`}>▼</span>
            </button>
          </div>

          {/* Old News Archive */}
          {showOldNews && (
            <div className="old-news-archive">
              <h4>Ältere Nachrichten</h4>
              <div className="old-news-grid">
                {oldNews.map(news => (
                  <div key={news.id} className="old-news-item">
                    <div className="news-date">{news.date}</div>
                    <h5>{news.title}</h5>
                    <p>{news.excerpt}</p>
                    {news.content !== news.excerpt && (
                      <button className="read-more-btn-small">
                        Weiterlesen →
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Teaser Sections */}
        <div className="teasers-section">
          <h3>Entdecken Sie den TC-Wehen</h3>
          <div className="teasers-grid">

            {/* Training Teaser */}
            <div className="teaser-card">
              <div className="teaser-image">
                <img src="/assets/Plätze3.PNG" alt="Training auf den Tennisplätzen" />
              </div>
              <div className="teaser-content">
                <h4>Professionelles Training</h4>
                <p>
                  Von Kindern bis Erwachsene - Frank Prätorius bietet individuelles Training
                  für alle Spielstärken. Schnupperstunden möglich!
                </p>
                <button className="teaser-button" onClick={() => navigate('training')}>
                  Mehr zum Training →
                </button>
              </div>
            </div>

            {/* Verein Teaser */}
            <div className="teaser-card">
              <div className="teaser-image">
                <img src="/assets/Plätze2.PNG" alt="Vereinsanlage TC-Wehen" />
              </div>
              <div className="teaser-content">
                <h4>Unser Verein</h4>
                <p>
                  Seit 1978 ein familienfreundlicher Tennisverein mit über 120 Mitgliedern.
                  Gemeinschaft und sportliche Fairness stehen bei uns im Mittelpunkt.
                </p>
                <button className="teaser-button" onClick={() => navigate('verein')}>
                  Verein kennenlernen →
                </button>
              </div>
            </div>

            {/* Anlage Teaser */}
            <div className="teaser-card">
              <div className="teaser-image">
                <img src="/assets/Terrassenansicht-Plätze.PNG" alt="Blick von der Terrasse" />
              </div>
              <div className="teaser-content">
                <h4>Unsere Anlage</h4>
                <p>
                  6 gepflegte Sandplätze, eine Ballwand und ein gemütliches Vereinsheim
                  mit Terrasse - perfekt für entspannte Stunden nach dem Spiel.
                </p>
                <button className="teaser-button" onClick={() => navigate('verein-anlage')}>
                  Anlage entdecken →
                </button>
              </div>
            </div>

            {/* Kontakt Teaser */}
            <div className="teaser-card">
              <div className="teaser-image">
                <img src="/assets/Ball.PNG" alt="Tennisball" />
              </div>
              <div className="teaser-content">
                <h4>Kontakt & Öffnungszeiten</h4>
                <p>
                  Haben Sie Fragen oder möchten uns besuchen? Hier finden Sie alle
                  Kontaktdaten und Öffnungszeiten unserer Anlage.
                </p>
                <button className="teaser-button" onClick={() => navigate('kontakt')}>
                  Kontakt aufnehmen →
                </button>
              </div>
            </div>

            {/* Medenspiele Teaser */}
            <div className="teaser-card">
              <div className="teaser-image">
                <img src="/assets/Plätze.PNG" alt="Wettkampf auf den Tennisplätzen" />
              </div>
              <div className="teaser-content">
                <h4>Medenspiele</h4>
                <p>
                  Verfolgen Sie die Ergebnisse unserer Mannschaften in den aktuellen
                  Medenrunden. Spannende Matches und tolle Erfolge!
                </p>
                <button className="teaser-button" onClick={() => navigate('medenspiele')}>
                  Zu den Ergebnissen →
                </button>
              </div>
            </div>

            {/* Platz buchen Teaser */}
            <div className="teaser-card teaser-highlight">
              <div className="teaser-image">
                <img src="/assets/Obenansicht-Plätze.PNG" alt="Luftaufnahme der Tennisplätze" />
              </div>
              <div className="teaser-content">
                <h4>Platz buchen</h4>
                <p>
                  Buchen Sie Ihren Tennisplatz bequem online! Unser Buchungssystem
                  ist rund um die Uhr verfügbar - einfach und schnell.
                </p>
                <button className="teaser-button teaser-button-primary" onClick={() => navigate('buchung')}>
                  Jetzt buchen →
                </button>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );

  const TrainingPage = () => (
    <div className="page training-page">
      <div className="page-container">
        {/* Hero Section */}
        <div className="page-hero">
          <div className="hero-content">
            <h1>Professionelles Training für alle Altersgruppen</h1>
            <p>Von Kindern bis Erwachsene - Frank Prätorius bietet individuelles Training für alle Spielstärken. Schnupperstunden möglich!</p>
          </div>
        </div>

        {/* Training Categories Grid */}
        <div className="training-categories">
          <div className="category-card">
            <div className="category-image">
              <img src="/assets/Plätze3.PNG" alt="Kindertraining" />
            </div>
            <div className="category-content">
              <h3>Kindertraining (5-12 Jahre)</h3>
              <p>Spielerischer Einstieg in den Tennissport mit altersgerechten Schlägern und Bällen. Koordination und Ballgefühl entwickeln in Kleingruppen.</p>
              <ul className="category-features">
                <li>Spielerischer Einstieg</li>
                <li>Koordination und Ballgefühl</li>
                <li>Kleingruppen (max. 4 Kinder)</li>
                <li>Altersgerechte Ausrüstung</li>
              </ul>
            </div>
          </div>

          <div className="category-card">
            <div className="category-image">
              <img src="/assets/Plätze2.PNG" alt="Jugendtraining" />
            </div>
            <div className="category-content">
              <h3>Jugendtraining (13-18 Jahre)</h3>
              <p>Technikverfeinerung und Taktikschulung für ambitionierte Nachwuchstalente. Wettkampfvorbereitung und Turnierbegleitung inklusive.</p>
              <ul className="category-features">
                <li>Technikverfeinerung</li>
                <li>Wettkampfvorbereitung</li>
                <li>Konditions- und Krafttraining</li>
                <li>Turnierbegleitung</li>
              </ul>
            </div>
          </div>

          <div className="category-card">
            <div className="category-image">
              <img src="/assets/Plätze.PNG" alt="Erwachsenentraining" />
            </div>
            <div className="category-content">
              <h3>Erwachsenentraining</h3>
              <p>Für Anfänger und Fortgeschrittene - lernen Sie die Grundschläge oder optimieren Sie Ihre Technik. Flexible Terminvereinbarung möglich.</p>
              <ul className="category-features">
                <li>Anfänger: Grundschläge erlernen</li>
                <li>Fortgeschrittene: Technikoptimierung</li>
                <li>Spielpraxis und Matchtraining</li>
                <li>Flexible Terminvereinbarung</li>
              </ul>
            </div>
          </div>

          <div className="category-card">
            <div className="category-image">
              <img src="/assets/Terrassenansicht-Plätze.PNG" alt="Einzeltraining" />
            </div>
            <div className="category-content">
              <h3>Einzeltraining</h3>
              <p>Individuelle Betreuung für schnelle Fortschritte. Gezieltes Techniktraining mit flexibler Termingestaltung nach Ihren Wünschen.</p>
              <ul className="category-features">
                <li>Individuelle Betreuung</li>
                <li>Gezieltes Techniktraining</li>
                <li>Schnelle Fortschritte</li>
                <li>Flexible Termingestaltung</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Trainer Section */}
        <div className="trainer-section">
          <div className="trainer-content">
            <div className="trainer-info">
              <img src="/assets/Tennisschule-Prätorius.PNG" alt="Tennisschule Prätorius" className="training-logo" />
              <h3>Frank Prätorius</h3>
              <p className="trainer-title">Lizenzierter Tennistrainer & Vereinstrainer</p>
              <p className="trainer-description">
                Mit über 15 Jahren Erfahrung im Tennissport biete ich professionelles Training
                für alle Altersgruppen und Spielstärken. Meine Philosophie: Tennis soll Spaß machen
                und gleichzeitig technisch fundiert vermittelt werden.
              </p>
            </div>
            <div className="trainer-image">
              <div className="trainer-placeholder">
                <p>Trainer-Foto folgt in Kürze</p>
              </div>
            </div>
          </div>
        </div>

        {/* Contact & Pricing Section */}
        <div className="training-contact-section">
          <div className="contact-grid">
            <div className="contact-card">
              <h4>Training buchen</h4>
              <div className="contact-details">
                <p><strong>📞 Telefon:</strong> [Telefonnummer Frank Prätorius]</p>
                <p><strong>📧 Email:</strong> <EMAIL></p>
                <p><strong>📱 WhatsApp:</strong> [Handynummer]</p>
                <p><strong>🕐 Erreichbarkeit:</strong> Mo-Fr 16:00-20:00</p>
              </div>
              <p className="booking-note">Schnupperstunden für Anfänger möglich! Einfach anrufen und Termin vereinbaren.</p>
            </div>

            <div className="pricing-card">
              <h4>Preise (Beispiel)</h4>
              <ul className="price-list">
                <li>Einzelstunde (60 Min): 45€</li>
                <li>Gruppentraining (4er): 20€/Person</li>
                <li>Kindertraining (45 Min): 15€</li>
                <li>10er-Karte Einzeltraining: 400€</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const VereinPage = () => (
    <div className="page verein-page">
      <div className="page-container">
        {/* Hero Section */}
        <div className="page-hero">
          <div className="hero-content">
            <h1>TC-Wehen e.V.</h1>
            <p>Herzlich willkommen in unserem Tennisclub, herrlich gelegen in Wehen mit 6 gepflegten Sandplätzen und einem gemütlichen Vereinsheim.</p>
          </div>
        </div>

        {/* Club Gallery */}
        <div className="club-gallery">
          <div className="gallery-main">
            <img src="/assets/Plätze2.PNG" alt="Vereinsanlage TC-Wehen" />
          </div>
          <div className="gallery-grid">
            <img src="/assets/Plätze.PNG" alt="Tennisplätze" />
            <img src="/assets/Terrassenansicht-Plätze.PNG" alt="Terrassenansicht" />
            <img src="/assets/Obenansicht-Plätze.PNG" alt="Luftaufnahme" />
          </div>
        </div>

        {/* Club Info Section */}
        <div className="club-info-section">
          <div className="club-story">
            <h3>Über den TC-Wehen</h3>
            <p>
              Der Tennis-Club Wehen e.V. wurde 1978 gegründet und ist seitdem ein fester Bestandteil
              der Gemeinde Wehen. Mit über 40 Jahren Vereinsgeschichte haben wir uns zu einem
              modernen und familienfreundlichen Tennisverein entwickelt.
            </p>
            <p>
              Unser Verein steht für Gemeinschaft, sportliche Fairness und die Förderung des
              Tennissports für alle Altersgruppen. Von Kindern bis zu Senioren - bei uns ist
              jeder willkommen!
            </p>
          </div>
        </div>

        {/* Club Highlights */}
        <div className="club-highlights">
          <div className="highlight-card">
            <div className="highlight-icon">🎾</div>
            <h4>Über 120 Mitglieder</h4>
            <p>Aktive Mitglieder aller Altersgruppen spielen regelmäßig auf unseren Plätzen</p>
          </div>
          <div className="highlight-card">
            <div className="highlight-icon">🏆</div>
            <h4>Mannschaften</h4>
            <p>Mehrere Herren-, Damen- und Jugendmannschaften vertreten uns in den Medenrunden</p>
          </div>
          <div className="highlight-card">
            <div className="highlight-icon">🎉</div>
            <h4>Veranstaltungen</h4>
            <p>Vereinsturniere, Sommerfest und gesellige Abende stärken unsere Gemeinschaft</p>
          </div>
          <div className="highlight-card">
            <div className="highlight-icon">👨‍👩‍👧‍👦</div>
            <h4>Familienfreundlich</h4>
            <p>Spezielle Angebote für Familien und Kinder machen Tennis für alle zugänglich</p>
          </div>
        </div>

        {/* Board Section */}
        <div className="board-section">
          <h3>Unser Vorstand</h3>
          <div className="board-grid">
            <div className="board-member">
              <div className="member-photo-placeholder">
                <p>Foto folgt</p>
              </div>
              <h4>1. Vorsitzender</h4>
              <p className="member-name">[Name]</p>
              <p className="member-role">Vereinsführung und Repräsentation</p>
            </div>
            <div className="board-member">
              <div className="member-photo-placeholder">
                <p>Foto folgt</p>
              </div>
              <h4>2. Vorsitzender</h4>
              <p className="member-name">[Name]</p>
              <p className="member-role">Stellvertretung und Projekte</p>
            </div>
            <div className="board-member">
              <div className="member-photo-placeholder">
                <p>Foto folgt</p>
              </div>
              <h4>Kassenwart</h4>
              <p className="member-name">[Name]</p>
              <p className="member-role">Finanzen und Buchhaltung</p>
            </div>
            <div className="board-member">
              <div className="member-photo-placeholder">
                <p>Foto folgt</p>
              </div>
              <h4>Sportwart</h4>
              <p className="member-name">[Name]</p>
              <p className="member-role">Sportbetrieb und Turniere</p>
            </div>
          </div>
        </div>

        {/* Membership Section */}
        <div className="membership-section">
          <div className="membership-content">
            <div className="membership-info">
              <h3>Werde Teil unseres Teams</h3>
              <p>
                Interessiert an einer Mitgliedschaft? Wir freuen uns über neue Gesichter!
                Als Mitglied genießen Sie viele Vorteile:
              </p>
              <ul className="membership-benefits">
                <li>Kostenlose Platznutzung während der Öffnungszeiten</li>
                <li>Teilnahme an Vereinsturnieren und Medenrunden</li>
                <li>Gesellige Vereinsveranstaltungen</li>
                <li>Professionelle Trainingsmöglichkeiten</li>
              </ul>
              <p className="membership-note">
                <strong>Schnuppern erwünscht!</strong> Kommen Sie einfach vorbei und lernen Sie
                unseren Verein kennen. Gerne können Sie auch ein Probetraining vereinbaren.
              </p>
            </div>
            <div className="membership-image">
              <img src="/assets/Plätze3.PNG" alt="Vereinsleben" />
            </div>
          </div>
        </div>

        {/* Documents Section */}
        <div className="documents-section">
          <h3>Vereinsdokumente</h3>
          <div className="documents-grid">
            <div className="document-card" onClick={() => navigate('mitglied-werden')}>
              <div className="document-icon">📄</div>
              <h4>Mitglied werden</h4>
              <p>Informationen zur Mitgliedschaft</p>
            </div>
            <div className="document-card" onClick={() => navigate('satzung')}>
              <div className="document-icon">📋</div>
              <h4>Vereinssatzung</h4>
              <p>Offizielle Vereinssatzung</p>
            </div>
            <div className="document-card" onClick={() => navigate('hand-spanndienst')}>
              <div className="document-icon">🔧</div>
              <h4>Hand- und Spanndienst</h4>
              <p>Arbeitsstunden für Mitglieder</p>
            </div>
            <div className="document-card" onClick={() => navigate('mach-mit')}>
              <div className="document-icon">🤝</div>
              <h4>Mach Mit!</h4>
              <p>Mithilfe im Verein</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const AnlagePage = () => (
    <div className="page anlage-page">
      <div className="page-container">
        {/* Hero Section */}
        <div className="page-hero">
          <div className="hero-content">
            <h1>Unsere Tennisanlage</h1>
            <p>6 gepflegte Sandplätze, eine Ballwand und ein gemütliches Vereinsheim mit Terrasse - perfekt für entspannte Stunden nach dem Spiel.</p>
          </div>
        </div>

        {/* Main Facility Image */}
        <div className="facility-hero-image">
          <img src="/assets/Obenansicht-Plätze.PNG" alt="Luftaufnahme unserer Tennisanlage" />
          <div className="image-caption">
            <p>Luftaufnahme unserer Tennisanlage mit 6 Sandplätzen</p>
          </div>
        </div>

        {/* Facility Features */}
        <div className="facility-features">
          <div className="feature-card">
            <div className="feature-icon">🎾</div>
            <h3>6 Sandplätze</h3>
            <p>Gepflegte Sandplätze in herrlicher Lage</p>
          </div>
          <div className="feature-card">
            <div className="feature-icon">🏠</div>
            <h3>Vereinsheim</h3>
            <p>Gemütliches Vereinsheim mit Terrasse</p>
          </div>
          <div className="feature-card">
            <div className="feature-icon">🎯</div>
            <h3>Ballwand</h3>
            <p>Trainingsmöglichkeit für alle Mitglieder</p>
          </div>
          <div className="feature-card">
            <div className="feature-icon">🌅</div>
            <h3>Terrasse</h3>
            <p>Entspannung mit Blick auf die Plätze</p>
          </div>
        </div>

        {/* Image Gallery */}
        <div className="facility-gallery">
          <h3>Impressionen unserer Anlage</h3>
          <div className="gallery-grid">
            <div className="gallery-item">
              <img src="/assets/Plätze.PNG" alt="Tennisplätze Hauptansicht" />
              <div className="gallery-caption">Hauptansicht der Tennisplätze</div>
            </div>
            <div className="gallery-item">
              <img src="/assets/Plätze2.PNG" alt="Tennisplätze Seitenansicht" />
              <div className="gallery-caption">Seitenansicht der Anlage</div>
            </div>
            <div className="gallery-item">
              <img src="/assets/Plätze3.PNG" alt="Tennisplätze Detailansicht" />
              <div className="gallery-caption">Detailansicht der Plätze</div>
            </div>
            <div className="gallery-item">
              <img src="/assets/Terrassenansicht-Plätze.PNG" alt="Blick von der Terrasse" />
              <div className="gallery-caption">Blick von der Terrasse</div>
            </div>
          </div>
        </div>

        {/* Description */}
        <div className="facility-description">
          <div className="description-content">
            <h3>Willkommen auf unserer Anlage</h3>
            <p>
              Hier beim TC-Wehen haben wir 6 schöne und gepflegte Sandplätze und eine Ballwand.
              Sonne lässt sich bis zum Abend wunderschön auf unserer Terrasse bewundern!
              Kommt doch mal vorbei!
            </p>
            <p>
              Unsere Anlage bietet optimale Bedingungen für Tennis auf höchstem Niveau.
              Die Plätze werden regelmäßig gepflegt und sind das ganze Jahr über in
              bestem Zustand.
            </p>
          </div>
        </div>

        {/* Location Section */}
        <div className="location-section">
          <div className="location-content">
            <div className="location-info">
              <h3>Anfahrt & Lage</h3>
              <div className="address-card">
                <h4>Tennis-Club Wehen e.V.</h4>
                <p className="address-line">Platter Straße 89</p>
                <p className="address-line">65232 Taunusstein</p>
              </div>
              <div className="directions">
                <h4>Anfahrt mit dem Auto</h4>
                <p>A3 Ausfahrt Wiesbaden-Erbenheim, dann B417 Richtung Taunusstein-Wehen. In Wehen der Platter Straße folgen.</p>
                <h4>Öffentliche Verkehrsmittel</h4>
                <p>Bus Linie 274 bis Haltestelle "Wehen Ortsmitte", dann 5 Minuten Fußweg über die Platter Straße.</p>
              </div>
            </div>
            <div className="location-map">
              <iframe
                src="https://maps.google.com/maps?q=50.151,8.207+(TC-Wehen+e.V.+Tennisclub)&t=&z=16&ie=UTF8&iwloc=&output=embed"
                width="100%"
                height="300"
                style={{border: 0, borderRadius: '8px'}}
                allowFullScreen=""
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="TC-Wehen e.V. - Tennisclub in Wehen, Taunusstein"
              ></iframe>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const KontaktPage = () => (
    <div className="page kontakt-page">
      <div className="page-container">
        {/* Hero Section */}
        <div className="page-hero">
          <div className="hero-content">
            <h1>Kontakt</h1>
            <p>Haben Sie Fragen oder möchten uns besuchen? Hier finden Sie alle Kontaktdaten und Öffnungszeiten unserer Anlage.</p>
          </div>
        </div>

        {/* Main Contact Info */}
        <div className="main-contact-section">
          <div className="contact-info-grid">
            <div className="contact-info-card">
              <div className="contact-icon">📞</div>
              <h3>Telefon</h3>
              <p className="contact-detail">[Platzhalter Telefonnummer]</p>
              <p className="contact-note">Erreichbarkeit: Mo-Fr 18:00-20:00</p>
            </div>
            <div className="contact-info-card">
              <div className="contact-icon">📧</div>
              <h3>E-Mail</h3>
              <p className="contact-detail">[Platzhalter Club Email]</p>
              <p className="contact-note">Wir antworten innerhalb von 24 Stunden</p>
            </div>
            <div className="contact-info-card">
              <div className="contact-icon">📍</div>
              <h3>Adresse</h3>
              <p className="contact-detail">Tennis-Club Wehen e.V.</p>
              <p className="contact-detail">Platter Straße 89</p>
              <p className="contact-detail">65232 Taunusstein</p>
            </div>
          </div>
        </div>

        {/* Contact Persons */}
        <div className="contact-persons-section">
          <h3>Ihre Ansprechpartner</h3>
          <div className="persons-grid">
            <div className="person-card">
              <div className="person-photo-placeholder">
                <div className="person-icon">🏆</div>
              </div>
              <div className="person-info">
                <h4>Vereinsführung</h4>
                <p className="person-name">1. Vorsitzender: [Name]</p>
                <p className="person-contact">📞 [Telefonnummer]</p>
                <p className="person-contact">📧 [E-Mail]</p>
                <p className="person-role">Allgemeine Vereinsangelegenheiten, Mitgliedschaft</p>
              </div>
            </div>

            <div className="person-card">
              <div className="person-photo-placeholder">
                <div className="person-icon">🎾</div>
              </div>
              <div className="person-info">
                <h4>Sportbetrieb</h4>
                <p className="person-name">Sportwart: [Name]</p>
                <p className="person-contact">📞 [Telefonnummer]</p>
                <p className="person-contact">📧 [E-Mail]</p>
                <p className="person-role">Turniere, Medenrunden, Sportveranstaltungen</p>
              </div>
            </div>

            <div className="person-card">
              <div className="person-photo-placeholder">
                <div className="person-icon">👨‍🏫</div>
              </div>
              <div className="person-info">
                <h4>Training</h4>
                <p className="person-name">Frank Prätorius</p>
                <p className="person-contact">📞 [Telefonnummer Frank Prätorius]</p>
                <p className="person-contact">📧 <EMAIL></p>
                <p className="person-role">Tennistraining, Schnupperstunden</p>
              </div>
            </div>

            <div className="person-card">
              <div className="person-photo-placeholder">
                <div className="person-icon">🔧</div>
              </div>
              <div className="person-info">
                <h4>Anlage & Technik</h4>
                <p className="person-name">Platzwart: [Name]</p>
                <p className="person-contact">📞 [Telefonnummer]</p>
                <p className="person-contact">📧 [E-Mail]</p>
                <p className="person-role">Platzpflege, technische Probleme</p>
              </div>
            </div>
          </div>
        </div>

        {/* Opening Hours */}
        <div className="opening-hours-section">
          <h3>Öffnungszeiten</h3>
          <div className="hours-grid">
            <div className="hours-card">
              <div className="season-icon">🌞</div>
              <h4>Sommersaison (April - Oktober)</h4>
              <div className="hours-list">
                <div className="hours-item">
                  <span className="day">Montag - Freitag:</span>
                  <span className="time">08:00 - 22:00 Uhr</span>
                </div>
                <div className="hours-item">
                  <span className="day">Samstag - Sonntag:</span>
                  <span className="time">08:00 - 22:00 Uhr</span>
                </div>
                <div className="hours-item">
                  <span className="day">Feiertage:</span>
                  <span className="time">08:00 - 22:00 Uhr</span>
                </div>
              </div>
              <p className="hours-note">Plätze sind bei Tageslicht bis 22:00 Uhr bespielbar</p>
            </div>

            <div className="hours-card">
              <div className="season-icon">❄️</div>
              <h4>Wintersaison (November - März)</h4>
              <div className="hours-list">
                <div className="hours-item">
                  <span className="day">Montag - Freitag:</span>
                  <span className="time">09:00 - 18:00 Uhr</span>
                </div>
                <div className="hours-item">
                  <span className="day">Samstag - Sonntag:</span>
                  <span className="time">09:00 - 18:00 Uhr</span>
                </div>
              </div>
              <p className="hours-note">Witterungsabhängig - bei Frost geschlossen</p>
            </div>
          </div>
        </div>

        {/* Directions */}
        <div className="directions-section">
          <h3>Anfahrt</h3>
          <div className="directions-grid">
            <div className="direction-card">
              <div className="direction-icon">🚗</div>
              <h4>Mit dem Auto</h4>
              <p>A3 Ausfahrt Wiesbaden-Erbenheim, dann B417 Richtung Taunusstein-Wehen. In Wehen der Platter Straße folgen. Kostenlose Parkplätze direkt am Vereinsgelände.</p>
            </div>
            <div className="direction-card">
              <div className="direction-icon">🚌</div>
              <h4>Mit öffentlichen Verkehrsmitteln</h4>
              <p>Bus Linie 274 bis Haltestelle "Wehen Ortsmitte", dann 5 Minuten Fußweg über die Platter Straße zum Tennisclub.</p>
            </div>
            <div className="direction-card">
              <div className="direction-icon">🚶‍♂️</div>
              <h4>Zu Fuß</h4>
              <p>Vom Ortskern Wehen sind es nur wenige Gehminuten über die Platter Straße. Der Tennisclub ist gut ausgeschildert.</p>
            </div>
          </div>
        </div>

        {/* Contact Note */}
        <div className="contact-note-section">
          <div className="note-content">
            <div className="note-icon">💬</div>
            <h3>Haben Sie Fragen?</h3>
            <p>
              Zögern Sie nicht, uns zu kontaktieren! Ob Interesse an einer Mitgliedschaft,
              Fragen zum Training oder einfach nur Neugier auf unseren Verein - wir freuen
              uns über jede Nachricht.
            </p>
            <p>
              <strong>Tipp:</strong> Kommen Sie doch einfach mal vorbei! Während der
              Öffnungszeiten ist immer jemand da, der Ihnen gerne weiterhilft.
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const ImpressumPage = () => (
    <div className="page impressum-page">
      <div className="page-container">
        {/* Hero Section */}
        <div className="page-hero">
          <div className="hero-content">
            <h1>Impressum</h1>
            <p>Rechtliche Angaben zum TC-Wehen e.V.</p>
          </div>
        </div>

        {/* Legal Content */}
        <div className="legal-content">
          <div className="legal-section">
            <h3>Angaben gemäß § 5 TMG</h3>
            <div className="legal-info">
              <p><strong>TC-Wehen e.V.</strong></p>
              <p>Platter Straße 89</p>
              <p>65232 Taunusstein</p>
            </div>
          </div>

          <div className="legal-section">
            <h3>Vertreten durch:</h3>
            <div className="legal-info">
              <p>1. Vorsitzender: [Name]</p>
              <p>2. Vorsitzender: [Name]</p>
            </div>
          </div>

          <div className="legal-section">
            <h3>Kontakt:</h3>
            <div className="legal-info">
              <p>Telefon: [Telefonnummer]</p>
              <p>E-Mail: [E-Mail-Adresse]</p>
            </div>
          </div>

          <div className="legal-section">
            <h3>Registereintrag:</h3>
            <div className="legal-info">
              <p>Eintragung im Vereinsregister</p>
              <p>Registergericht: [Amtsgericht]</p>
              <p>Registernummer: [VR-Nummer]</p>
            </div>
          </div>

          <div className="legal-section">
            <h3>Verantwortlich für den Inhalt nach § 55 Abs. 2 RStV:</h3>
            <div className="legal-info">
              <p>[Name des Verantwortlichen]</p>
              <p>[Adresse]</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const DatenschutzPage = () => (
    <div className="page datenschutz-page">
      <div className="page-container">
        {/* Hero Section */}
        <div className="page-hero">
          <div className="hero-content">
            <h1>Datenschutzerklärung</h1>
            <p>Informationen zum Schutz Ihrer persönlichen Daten</p>
          </div>
        </div>

        {/* Privacy Content */}
        <div className="privacy-content">
          <div className="privacy-section">
            <h3>1. Datenschutz auf einen Blick</h3>
            <p>Die folgenden Hinweise geben einen einfachen Überblick darüber, was mit Ihren personenbezogenen Daten passiert, wenn Sie unsere Website besuchen.</p>
          </div>

          <div className="privacy-section">
            <h3>2. Allgemeine Hinweise und Pflichtinformationen</h3>
            <h4>Datenschutz</h4>
            <p>Die Betreiber dieser Seiten nehmen den Schutz Ihrer persönlichen Daten sehr ernst. Wir behandeln Ihre personenbezogenen Daten vertraulich und entsprechend der gesetzlichen Datenschutzvorschriften sowie dieser Datenschutzerklärung.</p>
          </div>

          <div className="privacy-section">
            <h3>3. Datenerfassung auf unserer Website</h3>
            <h4>Wer ist verantwortlich für die Datenerfassung auf dieser Website?</h4>
            <p>Die Datenverarbeitung auf dieser Website erfolgt durch den Websitebetreiber. Dessen Kontaktdaten können Sie dem Impressum dieser Website entnehmen.</p>

            <h4>Wie erfassen wir Ihre Daten?</h4>
            <p>Ihre Daten werden zum einen dadurch erhoben, dass Sie uns diese mitteilen. Hierbei kann es sich z.B. um Daten handeln, die Sie in ein Kontaktformular eingeben.</p>
          </div>

          <div className="privacy-section">
            <h3>4. Buchungssystem</h3>
            <p>Für die Nutzung unseres Buchungssystems ist eine Registrierung erforderlich. Dabei werden folgende Daten erhoben:</p>
            <ul className="privacy-list">
              <li>Name und Vorname</li>
              <li>E-Mail-Adresse</li>
              <li>Buchungsdaten (Datum, Uhrzeit, Platz)</li>
            </ul>
            <p>Diese Daten werden ausschließlich zur Verwaltung der Platzbuchungen verwendet.</p>
          </div>

          <div className="privacy-section">
            <h3>5. Ihre Rechte</h3>
            <p>Sie haben jederzeit das Recht unentgeltlich Auskunft über Herkunft, Empfänger und Zweck Ihrer gespeicherten personenbezogenen Daten zu erhalten.</p>
          </div>
        </div>
      </div>
    </div>
  );

  const PlaceholderPage = ({ title }) => (
    <div className="page placeholder-page">
      <div className="page-container">
        {/* Hero Section */}
        <div className="page-hero">
          <div className="hero-content">
            <h1>{title}</h1>
            <p>Diese Seite ist noch in Bearbeitung.</p>
          </div>
        </div>

        {/* Placeholder Content */}
        <div className="placeholder-content">
          <div className="placeholder-card">
            <div className="placeholder-icon">🚧</div>
            <h3>Seite in Bearbeitung</h3>
            <p>Weitere Informationen folgen in Kürze.</p>
            <p>Bei Fragen wenden Sie sich gerne an uns über die <a href="#" onClick={() => navigate('kontakt')} className="contact-link">Kontakt-Seite</a>.</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPage = () => {
    switch(currentPage) {
      case 'home':
        return <HomePage />;
      case 'training':
        return <TrainingPage />;
      case 'verein':
        return <VereinPage />;
      case 'verein-anlage':
        return <AnlagePage />;
      case 'mitglied-werden':
        return <PlaceholderPage title="Mitglied werden" />;
      case 'satzung':
        return <PlaceholderPage title="Satzung" />;
      case 'hand-spanndienst':
        return <PlaceholderPage title="Hand- und Spanndienst" />;
      case 'mach-mit':
        return <PlaceholderPage title="Mach Mit!" />;
      case 'kontakt':
        return <KontaktPage />;
      case 'impressum':
        return <ImpressumPage />;
      case 'datenschutz':
        return <DatenschutzPage />;
      default:
        return <HomePage />;
    }
  };

  const Footer = () => (
    <footer className="footer">
      <div className="footer-content">
        <div className="footer-links">
          <a href="#" onClick={() => navigate('impressum')}>Impressum</a>
          <span>|</span>
          <a href="#" onClick={() => navigate('datenschutz')}>Datenschutz</a>
          <span>|</span>
          <a href="#" onClick={() => navigate('kontakt')}>Kontakt</a>
        </div>
        <div className="footer-text">
          <p>&copy; 2024 TC-Wehen e.V. - Alle Rechte vorbehalten</p>
        </div>
      </div>
    </footer>
  );

  return (
    <div className="landing-page">
      <Header />
      <main className="main">
        {renderPage()}
      </main>

      {/* Mobile Floating Action Button for Quick Booking */}
      {currentPage !== 'buchung' && (
        <button
          className="floating-action-btn"
          onClick={() => navigate('buchung')}
          aria-label="Schnell buchen"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V8H19V19ZM7 10H9V12H7V10ZM11 10H13V12H11V10ZM15 10H17V12H15V10Z" fill="currentColor"/>
          </svg>
          <span className="fab-text">Buchen</span>
        </button>
      )}

      <Footer />
    </div>
  );
};

export default LandingPage;
